import { type Dispatcher, interceptors } from 'undici'
import { createNoopInterceptor } from './noop'

export interface RetryInterceptorOptions {
    enabled?: boolean
    retry?: (
        err: Error,
        context: {
            state: { counter: number }
            opts: Dispatcher.DispatchOptions & { retryOptions?: RetryInterceptorOptions }
        },
        callback: (result?: Error | null) => void
    ) => void
    maxRetries?: number
    maxTimeout?: number
    minTimeout?: number
    timeoutFactor?: number
    retryAfter?: boolean
    methods?: Dispatcher.HttpMethod[]
    statusCodes?: number[]
    errorCodes?: string[]
}

export function createRetryInterceptor({ enabled = true, retries = 3, delay = 1000, backoff = 2, jitter = 0.1, maxDelay = 10_000 }: RetryInterceptorOptions = {}) {
    if (!enabled) {
        return createNoopInterceptor()
    }

    // Map old interface to new undici retry options
    const retryOptions = {
        maxRetries: retries,
        minTimeout: delay,
        maxTimeout: maxDelay,
        timeoutFactor: backoff,
        retryAfter: true,
        methods: ['GET', 'HEAD', 'OPTIONS', 'PUT', 'DELETE', 'TRACE'],
        statusCodes: [500, 502, 503, 504, 429],
        errorCodes: [
            'ECONNRESET',
            'ECONNREFUSED',
            'ENOTFOUND',
            'ENETDOWN',
            'ENETUNREACH',
            'EHOSTDOWN',
            'EHOSTUNREACH',
            'EPIPE',
            'UND_ERR_SOCKET',
        ],
    }

    return interceptors.retry(retryOptions)
}
