import { Emitter } from '@kdt310722/utils/event'
import { resolveNestedOptions } from '@kdt310722/utils/object'
import { interceptors, Pool } from 'undici'
import { parseUrl } from '../urls'

export interface SenderOptions {
    pool?: Pool.Options
    headers?: Record<string, string>
    retry?: SenderRetryOptions | boolean
}

export type SenderEvents = {
    connections: (count: number) => void
}

export class Sender extends Emitter<SenderEvents> {
    public readonly origin: string
    public readonly path: string

    protected readonly pool: Pool
    protected readonly headers: Record<string, string>
    protected readonly retryOptions: SenderRetryOptions

    public constructor(url: string, { pool, headers = {}, retry = true }: SenderOptions = {}) {
        super()

        const { origin, path } = parseUrl(url)

        this.origin = origin
        this.path = path
        this.pool = this.createPool(origin, pool)
        this.headers = headers
        this.retryOptions = resolveNestedOptions(retry) || { enabled: false }
    }

    public get stats() {
        return this.pool.stats
    }

    public async send() {}

    protected createPool(origin: string, options: Pool.Options = {}) {
        const pool = new Pool(origin, options)

        pool.on('connect', () => this.emit('connections', pool.stats.connected))
        pool.on('disconnect', () => this.emit(`connections`, pool.stats.connected))

        if (this.retryOptions.enabled) {
            pool.compose(this.createRetryInterceptor())
        }

        return pool
    }

    protected createRetryInterceptor() {
        return interceptors.retry({})
    }
}
